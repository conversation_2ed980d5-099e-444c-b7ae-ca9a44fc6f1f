using DG.Tweening;
using UnityEngine;
using TMPro;
using Whatwapp.MergeSolitaire.Game;
using _Tasks.Events;

namespace _Tasks.NewFeature.Visual
{
    public class BombBlockVisual : BlockVisual
    {
        [Header("Bomb Visual Settings")]
        [SerializeField] private Sprite _bombSprite;
        [SerializeField] private Color _bombColor = Color.red;
        [SerializeField] private string _bombSymbol = "💣";
        [SerializeField] private float _pulseIntensity = 0.1f;
        [SerializeField] private float _pulseDuration = 1.0f;
        [SerializeField] private bool _enableCountdownVisual = true;
        
        [Header("Countdown Visual")]
        [SerializeField] private Color _countdownColor = Color.yellow;
        [SerializeField] private float _countdownPulseSpeed = 2.0f;
        [SerializeField] private float _countdownScaleMultiplier = 1.2f;
        
        [Header("Explosion Visual")]
        [SerializeField] private Color _explosionColor = Color.white;
        [SerializeField] private float _explosionFlashDuration = 0.1f;
        [SerializeField] private float _explosionScaleMultiplier = 1.5f;
        
        private Sequence _pulseSequence;
        private Sequence _countdownSequence;
        private bool _isCountingDown = false;
        private bool _hasExploded = false;
        
        // References to base class components
        private SpriteRenderer _spriteRenderer;
        private TextMeshPro _text;
        private Vector3 _originalScale;
        private Color _originalColor;

        protected virtual void Awake()
        {
            // Get references to base class components
            _spriteRenderer = GetComponentInChildren<SpriteRenderer>();
            _text = GetComponentInChildren<TextMeshPro>();
            
            if (_spriteRenderer != null)
            {
                _originalColor = _spriteRenderer.color;
            }
            
            _originalScale = transform.localScale;
        }

        protected virtual void Start()
        {
            // Subscribe to bomb events
            EventManager.Subscribe<BombMovementStoppedEvent>(OnBombMovementStopped);
            EventManager.Subscribe<BombExplosionEvent>(OnBombExplosion);
            
            // Start idle pulse animation
            StartIdlePulse();
        }

        protected virtual void OnDestroy()
        {
            // Unsubscribe from events
            EventManager.Unsubscribe<BombMovementStoppedEvent>(OnBombMovementStopped);
            EventManager.Unsubscribe<BombExplosionEvent>(OnBombExplosion);
            
            // Clean up tweens
            _pulseSequence?.Kill();
            _countdownSequence?.Kill();
        }

        public override void Init(BlockValue value, BlockSeed seed)
        {
            // Initialize with bomb-specific visuals instead of card visuals
            _originalScale = transform.localScale;
            
            if (_spriteRenderer != null)
            {
                // Use bomb sprite if available, otherwise use seed-based sprite
                _spriteRenderer.sprite = _bombSprite != null ? _bombSprite : null;
                _spriteRenderer.color = _bombColor;
                _originalColor = _bombColor;
            }
            
            if (_text != null)
            {
                // Use bomb symbol instead of card value
                _text.text = _bombSymbol;
                _text.color = Color.white;
            }
        }

        /// <summary>
        /// Start subtle idle pulse animation to indicate this is a bomb
        /// </summary>
        private void StartIdlePulse()
        {
            if (_hasExploded || _isCountingDown) return;
            
            _pulseSequence?.Kill();
            _pulseSequence = DOTween.Sequence()
                .Append(transform.DOScale(_originalScale * (1f + _pulseIntensity), _pulseDuration * 0.5f))
                .Append(transform.DOScale(_originalScale, _pulseDuration * 0.5f))
                .SetLoops(-1, LoopType.Restart)
                .SetEase(Ease.InOutSine);
        }

        /// <summary>
        /// Start countdown visual effects when bomb movement stops
        /// </summary>
        private void StartCountdownVisual()
        {
            if (_hasExploded || !_enableCountdownVisual) return;
            
            _isCountingDown = true;
            
            // Stop idle pulse
            _pulseSequence?.Kill();
            
            // Start countdown effects
            _countdownSequence?.Kill();
            _countdownSequence = DOTween.Sequence();
            
            // Faster pulsing with color change
            if (_spriteRenderer != null)
            {
                _countdownSequence.Join(_spriteRenderer.DOColor(_countdownColor, 0.1f));
            }
            
            // Faster scale pulsing
            _countdownSequence.Append(
                DOTween.Sequence()
                    .Append(transform.DOScale(_originalScale * _countdownScaleMultiplier, _countdownPulseSpeed * 0.25f))
                    .Append(transform.DOScale(_originalScale, _countdownPulseSpeed * 0.25f))
                    .SetLoops(-1, LoopType.Restart)
                    .SetEase(Ease.InOutQuad)
            );
        }

        /// <summary>
        /// Play explosion visual effects
        /// </summary>
        private void PlayExplosionVisual()
        {
            if (_hasExploded) return;
            
            _hasExploded = true;
            
            // Stop all other animations
            _pulseSequence?.Kill();
            _countdownSequence?.Kill();
            
            // Flash white and scale up briefly
            var explosionSequence = DOTween.Sequence();
            
            if (_spriteRenderer != null)
            {
                explosionSequence.Join(_spriteRenderer.DOColor(_explosionColor, _explosionFlashDuration));
            }
            
            explosionSequence.Join(transform.DOScale(_originalScale * _explosionScaleMultiplier, _explosionFlashDuration))
                .AppendCallback(() => {
                    // The block will be destroyed by the game logic, so we don't need to do anything else
                });
        }

        /// <summary>
        /// Override base shake method to add bomb-specific effects
        /// </summary>
        public override void ShakeScale()
        {
            if (_hasExploded) return;
            
            // Use base shake but with bomb color flash
            base.ShakeScale();
            
            if (_spriteRenderer != null && !_isCountingDown)
            {
                _spriteRenderer.DOColor(_bombColor * 1.2f, 0.1f)
                    .OnComplete(() => _spriteRenderer.color = _originalColor);
            }
        }

        #region Event Handlers

        private void OnBombMovementStopped(BombMovementStoppedEvent eventData)
        {
            // Check if this event is for this bomb block
            if (eventData.BombBlock != null && eventData.BombBlock.transform == transform.parent)
            {
                StartCountdownVisual();
            }
        }

        private void OnBombExplosion(BombExplosionEvent eventData)
        {
            // Check if this event is for this bomb block
            if (eventData.SourceBomb != null && eventData.SourceBomb.transform == transform.parent)
            {
                PlayExplosionVisual();
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Manually trigger countdown visual (for testing or external control)
        /// </summary>
        public void TriggerCountdown()
        {
            StartCountdownVisual();
        }

        /// <summary>
        /// Manually trigger explosion visual (for testing or external control)
        /// </summary>
        public void TriggerExplosion()
        {
            PlayExplosionVisual();
        }

        /// <summary>
        /// Stop all visual effects and reset to idle state
        /// </summary>
        public void ResetVisual()
        {
            _hasExploded = false;
            _isCountingDown = false;
            
            _pulseSequence?.Kill();
            _countdownSequence?.Kill();
            
            transform.localScale = _originalScale;
            
            if (_spriteRenderer != null)
            {
                _spriteRenderer.color = _originalColor;
            }
            
            StartIdlePulse();
        }

        #endregion
    }
}
