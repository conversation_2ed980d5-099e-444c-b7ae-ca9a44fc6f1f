fileFormatVersion: 2
guid: dffef66376be4fa480fb02b19edbe903
TextureImporter:
  fileIDToRecycleName:
    21300000: EmojiOne_0
    21300002: EmojiOne_1
    21300004: EmojiOne_2
    21300006: EmojiOne_3
    21300008: EmojiOne_4
    21300010: EmojiOne_6
    21300012: EmojiOne_7
    21300014: EmojiOne_8
    21300016: EmojiOne_9
    21300018: EmojiOne_10
    21300020: EmojiOne_11
    21300022: EmojiOne_12
    21300024: EmojiOne_13
    21300026: EmojiOne_5
    21300028: EmojiOne_14
  externalObjects: {}
  serializedVersion: 5
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: -1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: -1
    aniso: -1
    mipBias: -1
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spritePixelsToUnits: 100
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 0
  textureShape: 1
  singleChannelComponent: 0
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  platformSettings:
  - serializedVersion: 2
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
  - serializedVersion: 2
    buildTarget: Standalone
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
  - serializedVersion: 2
    buildTarget: iPhone
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
  - serializedVersion: 2
    buildTarget: Android
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: EmojiOne_0
      rect:
        serializedVersion: 2
        x: 0
        y: 384
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4bcc36da2108f2c4ba3de5c921d25c3c
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: EmojiOne_1
      rect:
        serializedVersion: 2
        x: 128
        y: 384
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e9eea8093eaeaee4d901c4553f572c22
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: EmojiOne_2
      rect:
        serializedVersion: 2
        x: 256
        y: 384
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 49451da35411dcc42a3692e39b0fde70
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: EmojiOne_3
      rect:
        serializedVersion: 2
        x: 384
        y: 384
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f65709664b924904790c850a50ca82bc
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: EmojiOne_4
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5b92c568a5ec9ad4b9ed90e271f1c9a8
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: EmojiOne_6
      rect:
        serializedVersion: 2
        x: 256
        y: 256
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b10f2b48b7281594bb8a24a6511a35af
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: EmojiOne_7
      rect:
        serializedVersion: 2
        x: 384
        y: 256
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 10a600f9329dc2246a897e89f4d283cd
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: EmojiOne_8
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 66cffa363b90ab14787d8a5b90cf4502
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: EmojiOne_9
      rect:
        serializedVersion: 2
        x: 128
        y: 128
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 55cf3d409c9b89349b1e1bdc1cc224ad
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: EmojiOne_10
      rect:
        serializedVersion: 2
        x: 256
        y: 128
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2a9e58eaf96feef42bcefa1cf257193f
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: EmojiOne_11
      rect:
        serializedVersion: 2
        x: 384
        y: 128
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2489120affc155840ae6a7be2e93ce19
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: EmojiOne_12
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 412349a150598d14da4d7140df5c0286
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: EmojiOne_13
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a937464b42bb3634782dea34c6becb6c
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: EmojiOne_5
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b0f933b217682124dbfc5e6b89abe3d0
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: EmojiOne_14
      rect:
        serializedVersion: 2
        x: 128
        y: 256
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f7235c763afe4434e8bb666750a41096
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 3e32d8f5477abfc43b19066e8ad5032e
    vertices: []
    indices: 
    edges: []
    weights: []
  spritePackingTag: 
  userData: 
  assetBundleName: 
  assetBundleVariant: 
