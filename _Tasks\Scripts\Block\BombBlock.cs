﻿using System.Collections;
using System.Collections.Generic;
using _Tasks.Events;
using _Tasks.NewFeature.Controllers;
using _Tasks.NewFeature.Shared;
using UnityEngine;
using Whatwapp.MergeSolitaire.Game;
using Whatwapp.MergeSolitaire.Shared;

namespace _Tasks.NewFeature
{
    public class BombBlock : Whatwapp.MergeSolitaire.Game.Block, ISpecialBlock
    {
        [Header("Bomb Settings")]
        [SerializeField] private float _explosionDelay = 0.5f;
        [SerializeField] private int _explosionRadius = 1;
        [SerializeField] private bool _destroysSelf = true;
        [SerializeField] private bool _debugMode = false;

        public BlockType Type => BlockType.Bomb;
        public bool CanMerge => false; // Bombs don't merge

        private bool _hasExploded = false;
        private Coroutine _explosionCoroutine;

        public override void Init(BlockValue value, BlockSeed seed)
        {
            base.Init(value, seed);

            if (_debugMode)
                Debug.Log($"[BombBlock] Initialized at position {transform.position}");
        }

        public void OnPlacementComplete()
        {
            Debug.Log("[BombBlock] OnPlacementComplete called!"); // Temporary debug log

            var currentPos = GetCurrentCellPosition();

            // Broadcast bomb placed event
            var bombPlacedEvent = new BombPlacedEvent
            {
                BombBlock = this,
                Position = currentPos,
                ExplosionDelay = _explosionDelay
            };

            EventManager.Broadcast(bombPlacedEvent);

            if (_debugMode)
                Debug.Log($"[BombBlock] Placed at position {currentPos}");
        }

        public void OnMovementStopped()
        {
            Debug.Log("[BombBlock] OnMovementStopped called!"); // Temporary debug log

            if (_hasExploded) return;

            var currentPos = GetCurrentCellPosition();
            var affectedCells = GetAffectedCells();

            // Broadcast movement stopped event
            var movementStoppedEvent = new BombMovementStoppedEvent
            {
                BombBlock = this,
                FinalPosition = currentPos,
                AffectedCells = affectedCells
            };

            EventManager.Broadcast(movementStoppedEvent);

            // Start explosion countdown
            _explosionCoroutine = StartCoroutine(ExplodeAfterDelay());

            if (_debugMode)
                Debug.Log($"[BombBlock] Movement stopped at {currentPos}, starting explosion countdown");
        }

        private IEnumerator ExplodeAfterDelay()
        {
            // Wait for explosion delay
            yield return new WaitForSeconds(_explosionDelay);

            if (!_hasExploded)
            {
                Explode();
            }
        }

        private void Explode()
        {
            _hasExploded = true;

            var currentPos = GetCurrentCellPosition();

            // Use SpecialBlockController to get destroyable blocks
            var blocksToDestroy = SpecialBlockController.Instance.GetDestroyableBlocksInRadius(
                currentPos, _explosionRadius, this);

            if (_debugMode)
                Debug.Log($"[BombBlock] Exploding at {currentPos}, destroying {blocksToDestroy.Count} blocks");

            // Broadcast explosion event
            var explosionEvent = new BombExplosionEvent
            {
                ExplosionCenter = transform.position,
                DestroyedBlocks = blocksToDestroy,
                ExplosionRadius = _explosionRadius,
                SourceBomb = this
            };

            EventManager.Broadcast(explosionEvent);

            // Broadcast block destruction request
            if (blocksToDestroy.Count > 0)
            {
                var destructionEvent = new BlockDestructionRequestEvent
                {
                    BlocksToDestroy = blocksToDestroy,
                    ExplosionCenter = transform.position,
                    PlayDestructionEffects = true
                };

                EventManager.Broadcast(destructionEvent);
            }

            // Broadcast explosion effect event
            var effectEvent = new ExplosionEffectEvent
            {
                Position = transform.position,
                Radius = _explosionRadius,
                Intensity = 1.0f
            };

            EventManager.Broadcast(effectEvent);

            // Self-destruct after a brief delay
            StartCoroutine(SelfDestruct());
        }

        private List<Cell> GetAffectedCells()
        {
            if (SpecialBlockController.Instance == null)
            {
                Debug.LogError("[BombBlock] SpecialBlockController instance not found!");
                return new List<Cell>();
            }

            var currentPos = GetCurrentCellPosition();
            var affectedCells = SpecialBlockController.Instance.GetCellsInRadius(currentPos, _explosionRadius, true);

            if (_debugMode)
                Debug.Log($"[BombBlock] Found {affectedCells.Count} affected cells in radius {_explosionRadius}");

            return affectedCells;
        }

        private Vector2Int GetCurrentCellPosition()
        {
            if (SpecialBlockController.Instance == null)
            {
                Debug.LogError("[BombBlock] SpecialBlockController instance not found!");
                return Vector2Int.zero;
            }

            return SpecialBlockController.Instance.GetCellCoordinates(transform.position);
        }

        private IEnumerator SelfDestruct()
        {
            yield return new WaitForSeconds(0.2f);

            if (_debugMode)
                Debug.Log("[BombBlock] Self-destructing");

            if (_destroysSelf)
            {
                // Use SpecialBlockController to destroy this bomb
                var currentPos = GetCurrentCellPosition();
                if (SpecialBlockController.Instance != null)
                {
                    SpecialBlockController.Instance.DestroyBlockAtPosition(currentPos);
                }
                else
                {
                    // Fallback if controller is not available
                    Remove();
                }
            }
        }

        private void OnDestroy()
        {
            if (_explosionCoroutine != null)
            {
                StopCoroutine(_explosionCoroutine);
                _explosionCoroutine = null;
            }
        }

        // Public method for testing/debugging
        public void ForceExplode()
        {
            if (!_hasExploded)
            {
                if (_explosionCoroutine != null)
                {
                    StopCoroutine(_explosionCoroutine);
                }
                Explode();
            }
        }

        // Public getters for external systems
        public float ExplosionDelay => _explosionDelay;
        public int ExplosionRadius => _explosionRadius;
        public bool HasExploded => _hasExploded;
    }
}