using DG.Tweening;
using UnityEngine;
using TMPro;

namespace Whatwapp.MergeSolitaire.Game
{
    public class BlockVisual : MonoBehaviour
    {
        [<PERSON><PERSON>("Settings")]
        [SerializeField] protected ColorSettings _colorSettings;
        [SerializeField] protected AnimationSettings _animationSettings;
        
        [Header("References")]
        [SerializeField] protected SpriteRenderer _spriteRenderer;
        [SerializeField] protected TextMeshPro _text;
        
        
        private Vector3 _defaultScale;
        
        public virtual void Init(BlockValue value, BlockSeed seed)
        {
            _defaultScale = transform.localScale;
            _spriteRenderer.sprite = _colorSettings.GetBlockSprite(seed);
            _text.text = value.Symbol();
        }
        
        public virtual void ShakeScale()
        {
            transform.DOShakeScale(_animationSettings.BlockShakeDuration, _animationSettings.BlockShakeStrength)
                .OnComplete(() => transform.localScale = _defaultScale);
        }
    }
}