using _Tasks.Events;
using UnityEngine;
using Whatwapp.Core.Cameras;
using Whatwapp.Core.FSM;
using Whatwapp.Core.Audio;
using Whatwapp.MergeSolitaire.Game.GameStates;
using Whatwapp.MergeSolitaire.Game.UI;

namespace Whatwapp.MergeSolitaire.Game
{
    public class GameController : MonoBehaviour
    {
        [Header("References")] 
        [SerializeField] private Board board;
        [SerializeField] private GridBuilder _gridBuilder;
        [SerializeField] private TargetBoundedOrthographicCamera _targetBoundedCamera;
        [SerializeField] private BlockFactory _blockFactory;
        [SerializeField] private NextBlockController _nextBlockController;
        [SerializeField] private FoundationsController _foundationsController;
        
        [SerializeField] private ScoreBox _scoreBox;

        [Header("Settings")]
        [SerializeField] private AnimationSettings _animationSettings;

        private StateMachine _stateMachine;
        private SFXManager _sfxManager;

        private int _score = 0;
        private int _highScore = 0;
        private bool _isPaused = false;

        // State references for event-driven transitions
        private MoveBlocksState _moveBlocksState;
        private MergeBlocksState _mergeBlocksState;
        
        public bool IsPaused
        {
            get => _isPaused;
            set
            {
                _isPaused = value;
            }
        }

        public int Score
        {
            get => _score;
            set
            {
                _score = value;
                if (_score > _highScore)
                {
                    _highScore = _score;
                    PlayerPrefs.SetInt(Consts.PREFS_HIGHSCORE, _highScore);
                }
                _scoreBox.SetScore(_score);
                PlayerPrefs.SetInt(Consts.PREFS_LAST_SCORE, _score);
            }
        }

        private void Start()
        {
            _stateMachine = new StateMachine();
            _sfxManager = SFXManager.Instance;

            var generateLevel = new GenerateLevelState(this, board, _gridBuilder, _blockFactory, _targetBoundedCamera);
            var extractBlock = new ExtractBlockState(this, _nextBlockController, _sfxManager);
            _moveBlocksState = new MoveBlocksState(this, board, _animationSettings);
            _mergeBlocksState = new MergeBlocksState(this, board, _blockFactory, _foundationsController,
                _sfxManager, _animationSettings);
            var playBlockState = new PlayBlockState(this, board, _nextBlockController, _sfxManager);
            var gameOver = new GameOverState(this,  _sfxManager);
            var victory = new VictoryState(this,  _sfxManager);

            // Subscribe to bomb-related events
            EventManager.Subscribe<BlocksDestroyedEvent>(OnBlocksDestroyed);


            _stateMachine.AddTransition(generateLevel, extractBlock, 
                new Predicate(() => _gridBuilder.IsReady()));
            
            _stateMachine.AddTransition(extractBlock, _moveBlocksState,
                new Predicate(() => extractBlock.ExtractCompleted));

            _stateMachine.AddTransition(_moveBlocksState, _mergeBlocksState,
                new Predicate(() => _moveBlocksState.CanMoveBlocks() == false));

            _stateMachine.AddTransition(_mergeBlocksState, victory,
                new Predicate(() => _foundationsController.AllFoundationsCompleted));
            _stateMachine.AddTransition(_mergeBlocksState, _moveBlocksState,
                new Predicate(() =>
                    _mergeBlocksState.MergeCompleted && _mergeBlocksState.MergeCount > 0
                                               && ! _foundationsController.AllFoundationsCompleted));
            _stateMachine.AddTransition(_mergeBlocksState, playBlockState,
                new Predicate(() => _mergeBlocksState.MergeCompleted && _mergeBlocksState.MergeCount == 0
                                                               && ! _foundationsController.AllFoundationsCompleted));

            _stateMachine.AddTransition(playBlockState, extractBlock, 
                new Predicate(() =>  playBlockState.PlayBlockCompleted));
            _stateMachine.AddTransition(playBlockState, gameOver,
                new Predicate(() => playBlockState.GameOver));
            
            
            _stateMachine.SetState(generateLevel);
            
            _highScore = PlayerPrefs.GetInt(Consts.PREFS_HIGHSCORE, 0);
            Score = 0;
        }

        private void Update()
        {
            _stateMachine.Update();
        }

        private void FixedUpdate()
        {
            _stateMachine.FixedUpdate();
        }

        private void OnDestroy()
        {
            // Unsubscribe from events to prevent memory leaks
            EventManager.Unsubscribe<BlocksDestroyedEvent>(OnBlocksDestroyed);
        }

        /// <summary>
        /// Event handler for when blocks are destroyed (e.g., by bomb explosion)
        /// Forces the state machine to transition to MoveBlocksState to handle falling blocks
        /// </summary>
        private void OnBlocksDestroyed(BlocksDestroyedEvent eventData)
        {
            Debug.Log($"[GameController] Blocks destroyed event received. Count: {eventData.DestroyedBlockCount}");

            // Only trigger movement if blocks were actually destroyed and movement is required
            if (eventData.DestroyedBlockCount > 0 && eventData.RequiresMovement)
            {
                // Force transition to MoveBlocksState to handle falling blocks
                // This bypasses the normal state machine flow to immediately handle bomb aftermath
                _stateMachine.SetState(_moveBlocksState);

                Debug.Log("[GameController] Forced transition to MoveBlocksState after block destruction");
            }
        }
    }
}