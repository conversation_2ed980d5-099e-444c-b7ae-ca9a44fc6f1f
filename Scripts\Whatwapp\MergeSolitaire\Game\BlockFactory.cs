using UnityEngine;
using Whatwapp.Core.Utils;
using Whatwapp.MergeSolitaire.Shared;

namespace Whatwapp.MergeSolitaire.Game
{
    public class BlockFactory : MonoBehaviour
    {
        [Header("Prefabs")]
        [SerializeField] private Block _blockPrefab;
        [SerializeField] private Block _bombBlockPrefab;

        public Block Create(BlockValue value, BlockSeed seed)
        {
            var block = Instantiate(_blockPrefab, this.transform);
            block.Init(value, seed);
            return block;
        }
        
        public Block CreateStartingBlock()
        {
            var value = EnumUtils.GetRandom<BlockValue>(BlockValue.Ace, BlockValue.King);
            var seed = EnumUtils.GetRandom<BlockSeed>();
            return Create(value, seed);
        }

        /// <summary>
        /// Create a bomb block with specified settings
        /// </summary>
        public Block CreateBombBlock(BlockValue value = BlockValue.Ace, BlockSeed seed = BlockSeed.Spades)
        {
            if (_bombBlockPrefab == null)
            {
                Debug.LogError("[BlockFactory] Bomb block prefab is not assigned!");
                return Create(value, seed); // Fallback to normal block
            }

            var bombBlock = Instantiate(_bombBlockPrefab, this.transform);
            bombBlock.Init(value, seed);
            return bombBlock;
        }

        /// <summary>
        /// Create a block based on type (Normal or Bomb)
        /// </summary>
        public Block CreateBlock(BlockType blockType, BlockValue value, BlockSeed seed)
        {
            switch (blockType)
            {
                case BlockType.Bomb:
                    return CreateBombBlock(value, seed);
                case BlockType.Normal:
                default:
                    return Create(value, seed);
            }
        }
        
    }
}