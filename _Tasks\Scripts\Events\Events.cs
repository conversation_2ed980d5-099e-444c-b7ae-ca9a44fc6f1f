
using System.Collections.Generic;
using _Tasks.NewFeature;
using UnityEngine;

namespace _Tasks.Events
{
    public struct BombPlacedEvent
    {
        public BombBlock BombBlock;
        public Vector2Int Position;
        public float ExplosionDelay;
    }

    public struct BombMovementStoppedEvent
    {
        public BombBlock BombBlock;
        public Vector2Int FinalPosition;
        public List<Cell> AffectedCells;
    }

    public struct BombExplosionEvent
    {
        public Vector3 ExplosionCenter;
        public List<Block> DestroyedBlocks;
        public int ExplosionRadius;
        public BombBlock SourceBomb;
    }

    public struct BlockDestructionRequestEvent
    {
        public List<Block> BlocksToDestroy;
        public Vector3 ExplosionCenter;
        public bool PlayDestructionEffects;
    }

    public struct ExplosionEffectEvent
    {
        public Vector3 Position;
        public int Radius;
        public float Intensity;
    }

    /// <summary>
    /// Event broadcasted when blocks have been destroyed and the board needs to be updated
    /// This should trigger block movement and merging checks
    /// </summary>
    public struct BlocksDestroyedEvent
    {
        public int DestroyedBlockCount;
        public Vector3 DestructionCenter;
        public bool RequiresMovement;
        public bool RequiresMergeCheck;
    }
}